import React, { useState, useEffect } from 'react';
import { View, Text } from '@tarojs/components';
import { Button, Image, Checkbox, Stepper, SwipeAction  } from '@arco-design/mobile-react';
import { IconSearch, IconMore, IconArrowUp, IconDelete,IconPicture  } from "@arco-design/mobile-react/esm/icon";
import {  getCartList  } from "@/utils/api/common/common_user";
import YkNavBar from "@/components/ykNavBar/index";

import Taro from "@tarojs/taro";
import './index.less';

// 定义类型
interface CartDetail {
  id: number;
  userId: number;
  shoppingCartId: number;
  productColorId: number | null;
  productSpecificationsId: number | null;
  productSpecificationsName: string | null;
  productColorName: string | null;
  quantity: number;
  price: number;
  checked: boolean;
}

interface CartItem {
  id: number;
  dynamicsId: number;
  dynamicsUserId: number;
  dynamicsUserName: string;
  dynamicsUserAvatar: string;
  dynamicsContent: string;
  dynamicsImage: string;
  remark: string | null;
  type: number;
  checked: boolean;
  details: CartDetail[];
}

const initialShopData = [];

const Cart = () => {
  const [data, setData] = useState<CartItem[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [totalPrice, setTotalPrice] = useState(0);

  // 计算总件数和总价
  const calcTotal = (cartData: CartItem[]) => {
    let count = 0;
    let price = 0;
    cartData.forEach(item => {
      item.details.forEach(detail => {
        if (detail.checked) {
          count += detail.quantity;
          price += detail.quantity * detail.price;
        }
      });
    });
    setTotalCount(count);
    setTotalPrice(price);
  };

  // 全选状态
  const checkedAll = data.length > 0 && data.every(item => item.checked);

  // 单条选中
  const handleCheckItem = (idx: number) => {
    setData(prev => {
      const newData = prev.map((item, i) => {
        if (i === idx) {
          const checked = !item.checked;
          return {
            ...item,
            checked,
            details: item.details.map(d => ({ ...d, checked }))
          };
        }
        return item;
      });
      return newData;
    });
  };

  // 明细选中
  const handleCheckDetail = (itemIdx: number, detailIdx: number) => {
    setData(prev => {
      const newData = prev.map((item, i) => {
        if (i === itemIdx) {
          const details = item.details.map((d, dIdx) => {
            if (dIdx === detailIdx) {
              return { ...d, checked: !d.checked };
            }
            return d;
          });
          // item 选中=所有明细都选中
          const checked = details.every(d => d.checked);
          return { ...item, details, checked };
        }
        return item;
      });
      console.log(newData,'newData');
      return newData;
    });
  };

  // 全选
  const handleCheckAll = () => {
    setData(prev => {
      const checked = !checkedAll;
      const newData = prev.map(item => ({
        ...item,
        checked,
        details: item.details.map(d => ({ ...d, checked }))
      }));
      return newData;
    });
  };

  // 数量操作
  const handleCount = (itemIdx: number, detailIdx: number, type: 'add' | 'minus') => {
    setData(prev => {
      const newData = JSON.parse(JSON.stringify(prev));
      const detail = newData[itemIdx].details[detailIdx];
      if (type === 'add') detail.quantity += 1;
      if (type === 'minus' && detail.quantity > 1) detail.quantity -= 1;
      return newData;
    });
  };

  const addCheckedField = (list: any[]): CartItem[] => {
    return list.map(item => ({
      ...item,
      checked: typeof item.checked === 'boolean' ? item.checked : false,
      details: item.details.map((d: any) => ({
        ...d,
        checked: typeof d.checked === 'boolean' ? d.checked : false,
      })),
    }));
  };

  const getCartListData = async () => {
    const data = {
      pageNo: 1,
      pageSize: 10,
      userId: Taro.getStorageSync("userInfo").id,
    };
    const res = await getCartList(data);
    if(res.code == 0){
      const withChecked = addCheckedField(res.data.list);
      setData(withChecked);
    }
  };

  useEffect(() => {
    getCartListData();
  }, []);

  useEffect(() => {
    calcTotal(data);
  }, [data]);

  return (
    <View className="cart-page">
      {/* 头部 */}
      <YkNavBar  title="购物车" />
      {/* 列表 */}
      <View className="cart-list">
        {data.map((item, itemIdx) => (
          <View className="cart-shop" key={item.id}>
            <View className="cart-shop-header">
              <Checkbox checked={item.checked} onChange={() => handleCheckItem(itemIdx)} className="cart-checkbox"  />
              <Image className="cart-shop-tag" src={item.dynamicsUserAvatar} />
              <Text className="cart-shop-name">{item.dynamicsUserName}</Text>
            </View>
            <View className="cart-shop-products">
              <View className="cart-product-card">
              <View  style={{ borderBottom: '1px solid #eee', padding: '10px 0' }}>
                    <View className="cart-product-main">
                      <Image className="cart-product-img" src={item.dynamicsImage} />
                      <View className="cart-product-info">
                        <Text className="cart-product-title">{item.dynamicsContent}</Text>
                      </View>
                    </View>

                    {item.details.map((detail, detailIdx) => (
                      <SwipeAction
                        style={{ marginRight: '-1px' }}
                        rightActions={[
                          {
                            icon: <IconArrowUp />, text: "置顶", style: { backgroundColor: '#FF7D00', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center' },
                          },
                          {
                            icon: <IconDelete />, text: '删除', style: { backgroundColor: '#F53F3F', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center' },
                          }
                        ]}
                      >
                        <View style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <View style={{ display: 'flex', justifyContent: 'flex-start', alignItems: 'center' }}>
                            <Checkbox checked={detail.checked} onChange={() => handleCheckDetail(itemIdx, detailIdx)} className="cart-checkbox"  />
                            <View className="cart-product-meta">
                              <Text className="cart-product-size">{ detail.productSpecificationsName || detail.productColorName }</Text>
                              <Text className="cart-product-price">￥{detail.price}</Text>
                            </View>
                          </View>
                          <View className="cart-product-count">
                            <Stepper value={detail.quantity} step={1} onChange={val => handleCount(itemIdx, detailIdx, (val ?? detail.quantity) > detail.quantity ? 'add' : 'minus')} />
                          </View>
                        </View>
                      </SwipeAction>
                      ))}


                    <View className="cart-product-remark-row">
                      <Text className="cart-product-remark">备注：{item.remark || '-'}</Text>
                    </View>
                  </View>


                <View className="cart-product-extra-row">
                  <Button className="cart-product-send" type="default">转发</Button>
                </View>
              </View>
            </View>
          </View>
        ))}
      </View>
      {/* 底部栏 */}
      <View className="cart-footer">
        <View className="cart-footer-left">
          <Checkbox checked={checkedAll} onChange={handleCheckAll} className="cart-checkbox" value="" />
          <Text className="cart-footer-all">全选</Text>
          <Text className="cart-footer-manage">管理</Text>
        </View>
        <View className="cart-footer-info">
          <Text className="cart-footer-total">
            共 {totalCount} 件 <Text className="cart-footer-total-price">￥{totalPrice}</Text>
          </Text>
          <Text className="cart-footer-other">
            共优惠 <Text className="cart-footer-other-price">￥0</Text>
          </Text>
        </View>
        <View className="cart-footer-btn-wrap">
          <Button className="cart-footer-buy" type="ghost" inline size="small"
            onClick={() => {
              Taro.navigateTo({
                url: '/pages/order/pay/index'
              });
            }}
          >立即购买</Button>
        </View>
      </View>
    </View>
  );
};

export default Cart;
