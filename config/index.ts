import { defineConfig, type UserConfigExport } from "@tarojs/cli";
import TsconfigPathsPlugin from "tsconfig-paths-webpack-plugin";
import devConfig from "./dev";
import prodConfig from "./prod";
import path from "path";

// https://taro-docs.jd.com/docs/next/config#defineconfig-辅助函数
export default defineConfig<"webpack5">(async (merge) => {
  const baseConfig: UserConfigExport<"webpack5"> = {
    projectName: "taroApp",
    date: "2025-1-3",
    designWidth: 750,
    deviceRatio: {
      // 640: 2.34 / 2,
      750: 2,
      375:1
      // 375: 1 / 2,
      // 828: 1.81 / 2,
    },
    sourceRoot: "src",
    outputRoot: "dist",
    plugins: [
      // 热更新插件暂不使用
    ],
    defineConstants: {
      APP_NAME: JSON.stringify('ddxc'),
      APP_NAME_CN: JSON.stringify('东东相册'),
      BASE_URL:JSON.stringify('https://ppxc.fjpipixia.com'),
      BASE_URL_PPXC:JSON.stringify('https://ppxc.fjpipixia.com/'),
      KF_WS_URL:JSON.stringify('pipakf.fjpipixia.com/'),
    },
    copy: {
      patterns: [],
      options: {},
    },
    framework: "react",
    compiler: "webpack5",
    cache: {
      enable: true, // Webpack 持久化缓存配置，建议开启。默认配置请参考：https://docs.taro.zone/docs/config-detail#cache
    },
    alias: {
      '@': path.resolve(__dirname,'..','src'),
    },
    less: {
      resource: {
        '@': path.resolve(__dirname,'..','src'),
      },
    },
    mini: {
      postcss: {
        pxtransform: {
          enable: true,
          config: {
            unitPrecision: 1,
            onePxTransform: false,
            baseFontSize: 20, // 1rpx=2.34px
          },
        },
        cssModules: {
          enable: true, // 默认为 false，如需使用 css modules 功能，则设为 true
          config: {
            namingPattern: "module", // 转换模式，取值为 global/module
            generateScopedName: "[name]__[local]___[hash:base64:5]",
          },
        },
      },
      webpackChain(chain) {
        chain.resolve.plugin("tsconfig-paths").use(TsconfigPathsPlugin);
      },
    },
    h5: {
      publicPath: "./",
      staticDirectory: "static",
      output: {
        filename: "js/[name].[hash:8].js",
        chunkFilename: "js/[name].[chunkhash:8].js",
      },
      miniCssExtractPluginOption: {
        ignoreOrder: true,
        filename: "css/[name].[hash].css",
        chunkFilename: "css/[name].[chunkhash].css",
      },
      postcss: {
        autoprefixer: {
          enable: true,
          config: {},
        },
        cssModules: {
          enable: true, // 默认为 false，如需使用 css modules 功能，则设为 true
          config: {
            namingPattern: "module", // 转换模式，取值为 global/module
            generateScopedName: "[name]__[local]___[hash:base64:5]",
          },
        },
      },
      
      lessLoaderOption: {
        lessOptions: {
          javascriptEnabled: true,
          math: "always",
          modifyVars: {
            '@base-font-size': '20',
            '@use-css-vars': 1, // 开启css变量
            '@arco-dark-mode-selector': '.arco-theme-dark'
          },
          relativeUrls: true,
          paths: [path.resolve(__dirname, '../src')], // 添加这一行
        },
      },
      webpackChain(chain) {
        chain.resolve.plugin('tsconfig-paths').use(TsconfigPathsPlugin)
      }
    },
    rn: {
      appName: "taroDemo",
      postcss: {
        cssModules: {
          enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
        },
      },
    },
  };

  if (process.env.NODE_ENV === "development") {
    // 本地开发构建配置（不混淆压缩）
    return merge({}, baseConfig, devConfig);
  }
  // 生产构建配置（默认开启压缩混淆等）
  return merge({}, baseConfig, prodConfig);
});
